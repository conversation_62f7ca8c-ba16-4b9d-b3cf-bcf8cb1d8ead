# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# Add the parent directory to the path to find keyauth module
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath('.')), '..', 'src'))

block_cipher = None

a = Analysis(
    ['../steam_tools_gui.py'],
    pathex=[
        os.path.join(os.path.dirname(os.path.abspath('.')), '..', 'src'),
        os.path.dirname(os.path.abspath('.'))
    ],
    binaries=[],
    datas=[
        # Include keyauth.py from src folder
        (os.path.join('..', '..', 'src', 'keyauth.py'), '.'),
        # Include manifest file
        ('app.manifest', '.'),
    ],
    hiddenimports=[
        'keyauth',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'requests',
        'json',
        'threading',
        'hashlib',
        'winreg',
        'pathlib',
        'time',
        'os',
        'sys',
        'datetime',
        'traceback',
        'platform',
        'uuid',
        'inspect',
        'psutil',
        'ctypes',
        'customtkinter',
        'Crypto.Cipher.AES',
        'Crypto.Hash.SHA256',
        'Crypto.Util.Padding',
        'Crypto.Random',
        'Crypto.Protocol.KDF',
        'pycryptodome'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SteamToolsDownloader_v2',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for GUI application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon path here if you have one
    version='version_info.txt',  # Will be created separately
    uac_admin=True,  # Require administrator privileges
    uac_uiaccess=False  # Don't need UI access privileges
)
